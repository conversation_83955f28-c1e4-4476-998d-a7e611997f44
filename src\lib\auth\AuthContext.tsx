'use client';

import { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, getSessionSafe, db, Profile } from '../supabase';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  
  // Use refs to track initialization state and prevent duplicate calls
  const isInitialized = useRef(false);
  const isLoadingProfile = useRef(false);
  const lastAuthEvent = useRef<string>('');
  const currentUser = useRef<User | null>(null);
  const authStateSubscription = useRef<any>(null);

  const loadUserProfile = useCallback(async (userId: string, force = false) => {
    if (isLoadingProfile.current && !force) {
      console.log('Profile already loading, skipping...');
      return;
    }

    isLoadingProfile.current = true;

    try {
      console.log('Loading profile for user:', userId);
      
      let userProfile = await db.getProfile(userId);

      if (!userProfile) {
        // Profile doesn't exist, try to create one
        console.log('Profile not found, attempting to create one for user:', userId);

        // Get user email from Supabase auth
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.email) {
          userProfile = await db.createProfile(userId, user.email, user.user_metadata?.display_name);
          if (userProfile) {
            console.log('Profile created successfully for user:', userId);
          } else {
            console.error('Failed to create profile for user:', userId);
          }
        }
      }

      if (userProfile) {
        console.log('Profile loaded successfully:', userProfile.display_name);
        setProfile(userProfile);
        // Check if user is admin by role or email
        const isUserAdmin = userProfile.role === 'admin' || userProfile.email === '<EMAIL>';
        setIsAdmin(isUserAdmin);
        console.log('User admin status:', isUserAdmin);
      } else {
        console.log('No profile available for user:', userId);
        setProfile(null);
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setProfile(null);
      setIsAdmin(false);
    } finally {
      setLoading(false);
      isLoadingProfile.current = false;
    }
  }, []);

  useEffect(() => {
    let mounted = true;
    let retryCount = 0;
    const maxRetries = 2; // Reduced retries for faster experience

    const initializeAuth = async () => {
      if (isInitialized.current) {
        console.log('Auth already initialized, skipping...');
        return;
      }

      try {
        console.log('Initializing auth...');
        
        // Use the optimized session getter with shorter timeout
        const { data: { session }, error } = await Promise.race([
          getSessionSafe(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Session timeout')), 8000) // Reduced from 15s to 8s
          )
        ]);

        if (error) {
          console.error('Error getting session:', error);

          // Retry logic for session retrieval
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (retryCount < maxRetries && errorMessage.includes('timeout')) {
            retryCount++;
            console.log(`Session timeout, retrying... (${retryCount}/${maxRetries})`);
            setTimeout(() => initializeAuth(), 1000 * retryCount); // Faster retry
            return;
          }
          
          if (mounted) {
            setUser(null);
            currentUser.current = null;
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
            isInitialized.current = true;
          }
          return;
        }

        console.log('Session retrieved:', !!session?.user);

        if (mounted) {
          const newUser = session?.user ?? null;
          setUser(newUser);
          currentUser.current = newUser;

          if (session?.user) {
            await loadUserProfile(session.user.id);
          } else {
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
          
          isInitialized.current = true;
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        
        // Session recovery attempt
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`Auth initialization failed, retrying... (${retryCount}/${maxRetries})`);
          setTimeout(() => initializeAuth(), 2000 * retryCount); // Faster retry
          return;
        }
        
        if (mounted) {
          setUser(null);
          currentUser.current = null;
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
          isInitialized.current = true;
        }
      }
    };

    // Initialize auth state
    initializeAuth();

    // Listen for auth changes with better error handling and deduplication
    authStateSubscription.current = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (!mounted || !isInitialized.current) return;

        // Prevent duplicate events
        const eventKey = `${event}_${session?.user?.id || 'null'}`;
        if (lastAuthEvent.current === eventKey) {
          console.log('Duplicate auth event, ignoring:', eventKey);
          return;
        }
        lastAuthEvent.current = eventKey;

        // Handle specific auth events
        if (event === 'SIGNED_IN') {
          console.log('User signed in');
        }

        if (event === 'SIGNED_OUT') {
          console.log('User signed out');
          setUser(null);
          currentUser.current = null;
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
          return;
        }

        // Only set loading for significant auth changes that bring in a new user
        const shouldShowLoading = session?.user && !currentUser.current;
        if (shouldShowLoading) {
          setLoading(true);
        }

        const newUser = session?.user ?? null;
        setUser(newUser);
        currentUser.current = newUser;

        if (session?.user) {
          try {
            await loadUserProfile(session.user.id, event === 'SIGNED_IN');
          } catch (profileError) {
            console.error('Error loading profile after auth change:', profileError);
            // Don't sign out user if profile loading fails
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
        } else {
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
        }
      }
    );

    // Optimized auto-refresh - only when page is visible and less frequent
    const refreshInterval = setInterval(async () => {
      if (mounted && !document.hidden && isInitialized.current && currentUser.current) {
        try {
          // Use optimized session getter
          const { data: { session } } = await getSessionSafe();
          if (session) {
            console.log('Session auto-refresh check: active');
          } else {
            console.log('Session auto-refresh check: no active session');
          }
                  } catch (error) {
            console.warn('Session refresh check error:', error instanceof Error ? error.message : 'Unknown error');
          }
      }
    }, 15 * 60 * 1000); // Increased to 15 minutes for better performance

    return () => {
      mounted = false;
      if (authStateSubscription.current) {
        authStateSubscription.current.subscription.unsubscribe();
      }
      clearInterval(refreshInterval);
    };
  }, [loadUserProfile]);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Supabase sign in error:', error);

        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        } else if (error.message.includes('Email not confirmed')) {
          throw new Error('Please confirm your email address before signing in.');
        } else if (error.message.includes('Too many requests')) {
          throw new Error('Too many login attempts. Please wait a few minutes and try again.');
        } else {
          throw new Error(error.message || 'Failed to sign in. Please try again.');
        }
      }
    } catch (error) {
      setLoading(false);
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/confirm-email`
        }
      });

      if (error) {
        console.error('Supabase sign up error:', error);

        // Provide user-friendly error messages
        if (error.message.includes('User already registered')) {
          throw new Error('An account with this email already exists. Please sign in instead.');
        } else if (error.message.includes('Password should be at least')) {
          throw new Error('Password must be at least 6 characters long');
        } else if (error.message.includes('Invalid email')) {
          throw new Error('Please enter a valid email address');
        } else {
          throw new Error(error.message || 'Failed to create account. Please try again.');
        }
      }

      if (!data.user) {
        throw new Error('Failed to create user account. Please try again.');
      }

      // Check if there's a pending team application for this email
      try {
        const application = await db.getApplicationByEmail(email);
        if (application) {
          // Link the application to the new user account
          await db.markAccountCreated(application.id, data.user.id);
          console.log('Linked team application to new user account');
        }
      } catch (linkError) {
        console.error('Error linking application to account:', linkError);
        // Don't throw here as the account creation was successful
      }

      // Note: User will need to confirm email before they can sign in
    } catch (error: unknown) {
      setLoading(false);
      console.error('SignUp error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      console.log('Starting logout process...');
      
      // Clear local state first to provide immediate UI feedback
      setUser(null);
      currentUser.current = null;
      setProfile(null);
      setIsAdmin(false);
      
      // Sign out from Supabase with global scope
      const { error } = await supabase.auth.signOut({ scope: 'global' });

      if (error) {
        console.error('Supabase logout error:', error);
        // Don't throw here - still want to clear local storage
      }

      // Clear localStorage as a fallback
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase.auth.token');
        // Clear any other auth-related storage
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('supabase.auth.')) {
            localStorage.removeItem(key);
          }
        });
      }

      console.log('Logout completed successfully');

    } catch (error) {
      console.error('Logout error:', error);

      // Force clear everything even if logout fails
      setUser(null);
      currentUser.current = null;
      setProfile(null);
      setIsAdmin(false);
      setLoading(false);

      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.clear();
      }
    }
  };

  return (
    <AuthContext.Provider value={{ user, profile, loading, signIn, signUp, logout, isAdmin }}>
      {children}
    </AuthContext.Provider>
  );
};